{"_class_name": "WanImageToVideoPipeline", "_diffusers_version": "0.35.0.dev0", "boundary_ratio": 0.9, "image_encoder": [null, null], "image_processor": [null, null], "scheduler": ["diffusers", "UniPCMultistepScheduler"], "text_encoder": ["transformers", "UMT5EncoderModel"], "tokenizer": ["transformers", "T5TokenizerFast"], "transformer": ["diffusers", "WanTransformer3DModel"], "transformer_2": ["diffusers", "WanTransformer3DModel"], "vae": ["diffusers", "AutoencoderKLWan"]}