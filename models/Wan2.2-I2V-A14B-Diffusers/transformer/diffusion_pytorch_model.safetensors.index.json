{"metadata": {"total_size": 57155604736}, "weight_map": {"blocks.0.attn1.norm_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.norm_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.to_k.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.to_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.to_out.0.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.to_out.0.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.to_q.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.to_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.to_v.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn1.to_v.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.norm_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.norm_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.to_k.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.to_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.to_out.0.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.to_out.0.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.to_q.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.to_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.to_v.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.attn2.to_v.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.ffn.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.ffn.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.ffn.net.2.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.ffn.net.2.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.norm2.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.norm2.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.0.scale_shift_table": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.norm_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.norm_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.to_k.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.to_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.to_out.0.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.to_out.0.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.to_q.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.to_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.to_v.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn1.to_v.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.norm_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.norm_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.to_k.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.to_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.to_out.0.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.to_out.0.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.to_q.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.to_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.to_v.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.attn2.to_v.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.ffn.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.ffn.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.ffn.net.2.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.ffn.net.2.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.norm2.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.norm2.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.1.scale_shift_table": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.10.attn1.norm_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.norm_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.to_k.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.to_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.to_out.0.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.to_out.0.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.to_q.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.to_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.to_v.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn1.to_v.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.norm_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.norm_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.to_k.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.to_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.to_out.0.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.to_out.0.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.to_q.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.to_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.to_v.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.attn2.to_v.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.ffn.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.ffn.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.ffn.net.2.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.ffn.net.2.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.norm2.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.norm2.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.10.scale_shift_table": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.norm_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.norm_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.to_k.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.to_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.to_out.0.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.to_out.0.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.to_q.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.to_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.to_v.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn1.to_v.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.norm_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.norm_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.to_k.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.to_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.to_out.0.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.to_out.0.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.to_q.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.to_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.to_v.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.attn2.to_v.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.ffn.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.ffn.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.ffn.net.2.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.ffn.net.2.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.norm2.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.norm2.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.11.scale_shift_table": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.norm_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.norm_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.to_k.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.to_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.to_out.0.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.to_out.0.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.to_q.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.to_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.to_v.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn1.to_v.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.norm_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.norm_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.to_k.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.to_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.to_out.0.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.to_out.0.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.to_q.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.to_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.to_v.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.attn2.to_v.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.ffn.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.ffn.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.ffn.net.2.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.ffn.net.2.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.norm2.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.norm2.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.12.scale_shift_table": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.norm_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.norm_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.to_k.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.to_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.to_out.0.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.to_out.0.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.to_q.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.to_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.to_v.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn1.to_v.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn2.norm_k.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn2.norm_q.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.13.attn2.to_k.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.attn2.to_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.attn2.to_out.0.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.attn2.to_out.0.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.attn2.to_q.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.attn2.to_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.attn2.to_v.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.attn2.to_v.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.ffn.net.0.proj.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.ffn.net.0.proj.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.ffn.net.2.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.ffn.net.2.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.norm2.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.norm2.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.13.scale_shift_table": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.14.attn1.norm_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.norm_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.to_k.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.to_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.to_out.0.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.to_out.0.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.to_q.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.to_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.to_v.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn1.to_v.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.norm_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.norm_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.to_k.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.to_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.to_out.0.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.to_out.0.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.to_q.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.to_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.to_v.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.attn2.to_v.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.ffn.net.0.proj.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.ffn.net.0.proj.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.ffn.net.2.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.ffn.net.2.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.norm2.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.norm2.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.14.scale_shift_table": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.norm_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.norm_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.to_k.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.to_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.to_out.0.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.to_out.0.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.to_q.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.to_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.to_v.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn1.to_v.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.norm_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.norm_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.to_k.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.to_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.to_out.0.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.to_out.0.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.to_q.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.to_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.to_v.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.attn2.to_v.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.ffn.net.0.proj.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.ffn.net.0.proj.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.ffn.net.2.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.ffn.net.2.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.norm2.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.norm2.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.15.scale_shift_table": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.norm_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.norm_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.to_k.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.to_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.to_out.0.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.to_out.0.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.to_q.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.to_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.to_v.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn1.to_v.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.norm_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.norm_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.to_k.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.to_k.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.to_out.0.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.to_out.0.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.to_q.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.to_q.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.to_v.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.attn2.to_v.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.ffn.net.0.proj.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.ffn.net.0.proj.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.ffn.net.2.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.16.ffn.net.2.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.16.norm2.bias": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.norm2.weight": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.16.scale_shift_table": "diffusion_pytorch_model-00005-of-00012.safetensors", "blocks.17.attn1.norm_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.norm_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.to_k.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.to_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.to_out.0.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.to_out.0.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.to_q.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.to_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.to_v.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn1.to_v.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.norm_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.norm_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.to_k.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.to_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.to_out.0.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.to_out.0.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.to_q.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.to_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.to_v.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.attn2.to_v.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.ffn.net.0.proj.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.ffn.net.0.proj.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.ffn.net.2.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.ffn.net.2.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.norm2.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.norm2.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.17.scale_shift_table": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.norm_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.norm_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.to_k.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.to_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.to_out.0.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.to_out.0.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.to_q.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.to_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.to_v.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn1.to_v.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.norm_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.norm_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.to_k.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.to_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.to_out.0.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.to_out.0.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.to_q.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.to_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.to_v.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.attn2.to_v.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.ffn.net.0.proj.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.ffn.net.0.proj.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.ffn.net.2.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.ffn.net.2.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.norm2.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.norm2.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.18.scale_shift_table": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.norm_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.norm_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.to_k.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.to_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.to_out.0.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.to_out.0.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.to_q.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.to_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.to_v.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn1.to_v.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.norm_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.norm_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.to_k.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.to_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.to_out.0.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.to_out.0.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.to_q.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.to_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.to_v.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.attn2.to_v.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.ffn.net.0.proj.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.ffn.net.0.proj.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.ffn.net.2.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.ffn.net.2.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.norm2.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.norm2.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.19.scale_shift_table": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.2.attn1.norm_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.norm_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.to_k.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.to_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.to_out.0.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.to_out.0.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.to_q.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.to_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.to_v.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn1.to_v.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.norm_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.norm_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.to_k.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.to_k.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.to_out.0.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.to_out.0.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.to_q.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.to_q.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.to_v.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.attn2.to_v.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.ffn.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.ffn.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.ffn.net.2.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.2.ffn.net.2.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.2.norm2.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.norm2.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.2.scale_shift_table": "diffusion_pytorch_model-00001-of-00012.safetensors", "blocks.20.attn1.norm_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.norm_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.to_k.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.to_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.to_out.0.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.to_out.0.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.to_q.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.to_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.to_v.bias": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn1.to_v.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn2.norm_k.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn2.norm_q.weight": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.20.attn2.to_k.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.attn2.to_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.attn2.to_out.0.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.attn2.to_out.0.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.attn2.to_q.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.attn2.to_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.attn2.to_v.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.attn2.to_v.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.ffn.net.0.proj.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.ffn.net.0.proj.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.ffn.net.2.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.ffn.net.2.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.norm2.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.norm2.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.20.scale_shift_table": "diffusion_pytorch_model-00006-of-00012.safetensors", "blocks.21.attn1.norm_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.norm_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.to_k.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.to_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.to_out.0.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.to_out.0.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.to_q.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.to_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.to_v.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn1.to_v.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.norm_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.norm_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.to_k.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.to_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.to_out.0.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.to_out.0.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.to_q.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.to_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.to_v.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.attn2.to_v.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.ffn.net.0.proj.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.ffn.net.0.proj.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.ffn.net.2.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.ffn.net.2.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.norm2.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.norm2.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.21.scale_shift_table": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.norm_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.norm_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.to_k.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.to_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.to_out.0.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.to_out.0.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.to_q.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.to_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.to_v.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn1.to_v.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.norm_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.norm_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.to_k.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.to_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.to_out.0.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.to_out.0.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.to_q.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.to_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.to_v.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.attn2.to_v.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.ffn.net.0.proj.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.ffn.net.0.proj.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.ffn.net.2.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.ffn.net.2.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.norm2.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.norm2.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.22.scale_shift_table": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.norm_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.norm_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.to_k.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.to_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.to_out.0.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.to_out.0.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.to_q.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.to_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.to_v.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn1.to_v.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.norm_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.norm_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.to_k.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.to_k.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.to_out.0.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.to_out.0.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.to_q.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.to_q.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.to_v.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.attn2.to_v.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.ffn.net.0.proj.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.ffn.net.0.proj.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.ffn.net.2.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.23.ffn.net.2.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.23.norm2.bias": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.norm2.weight": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.23.scale_shift_table": "diffusion_pytorch_model-00007-of-00012.safetensors", "blocks.24.attn1.norm_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.norm_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.to_k.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.to_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.to_out.0.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.to_out.0.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.to_q.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.to_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.to_v.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn1.to_v.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.norm_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.norm_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.to_k.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.to_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.to_out.0.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.to_out.0.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.to_q.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.to_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.to_v.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.attn2.to_v.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.ffn.net.0.proj.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.ffn.net.0.proj.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.ffn.net.2.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.ffn.net.2.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.norm2.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.norm2.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.24.scale_shift_table": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.norm_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.norm_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.to_k.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.to_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.to_out.0.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.to_out.0.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.to_q.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.to_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.to_v.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn1.to_v.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.norm_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.norm_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.to_k.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.to_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.to_out.0.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.to_out.0.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.to_q.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.to_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.to_v.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.attn2.to_v.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.ffn.net.0.proj.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.ffn.net.0.proj.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.ffn.net.2.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.ffn.net.2.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.norm2.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.norm2.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.25.scale_shift_table": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.norm_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.norm_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.to_k.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.to_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.to_out.0.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.to_out.0.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.to_q.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.to_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.to_v.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn1.to_v.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.norm_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.norm_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.to_k.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.to_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.to_out.0.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.to_out.0.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.to_q.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.to_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.to_v.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.attn2.to_v.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.ffn.net.0.proj.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.ffn.net.0.proj.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.ffn.net.2.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.ffn.net.2.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.norm2.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.norm2.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.26.scale_shift_table": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.norm_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.norm_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.to_k.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.to_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.to_out.0.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.to_out.0.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.to_q.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.to_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.to_v.bias": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn1.to_v.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn2.norm_k.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn2.norm_q.weight": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.27.attn2.to_k.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.attn2.to_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.attn2.to_out.0.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.attn2.to_out.0.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.attn2.to_q.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.attn2.to_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.attn2.to_v.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.attn2.to_v.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.ffn.net.0.proj.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.ffn.net.0.proj.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.ffn.net.2.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.ffn.net.2.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.norm2.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.norm2.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.27.scale_shift_table": "diffusion_pytorch_model-00008-of-00012.safetensors", "blocks.28.attn1.norm_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.norm_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.to_k.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.to_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.to_out.0.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.to_out.0.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.to_q.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.to_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.to_v.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn1.to_v.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.norm_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.norm_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.to_k.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.to_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.to_out.0.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.to_out.0.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.to_q.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.to_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.to_v.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.attn2.to_v.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.ffn.net.0.proj.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.ffn.net.0.proj.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.ffn.net.2.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.ffn.net.2.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.norm2.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.norm2.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.28.scale_shift_table": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.norm_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.norm_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.to_k.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.to_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.to_out.0.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.to_out.0.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.to_q.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.to_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.to_v.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn1.to_v.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.norm_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.norm_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.to_k.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.to_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.to_out.0.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.to_out.0.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.to_q.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.to_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.to_v.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.attn2.to_v.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.ffn.net.0.proj.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.ffn.net.0.proj.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.ffn.net.2.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.ffn.net.2.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.norm2.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.norm2.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.29.scale_shift_table": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.3.attn1.norm_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.norm_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.to_k.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.to_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.to_out.0.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.to_out.0.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.to_q.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.to_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.to_v.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn1.to_v.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.norm_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.norm_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.to_k.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.to_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.to_out.0.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.to_out.0.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.to_q.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.to_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.to_v.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.attn2.to_v.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.ffn.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.ffn.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.ffn.net.2.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.ffn.net.2.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.norm2.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.norm2.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.3.scale_shift_table": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.30.attn1.norm_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.norm_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.to_k.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.to_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.to_out.0.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.to_out.0.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.to_q.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.to_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.to_v.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn1.to_v.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.norm_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.norm_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.to_k.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.to_k.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.to_out.0.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.to_out.0.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.to_q.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.to_q.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.to_v.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.attn2.to_v.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.ffn.net.0.proj.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.ffn.net.0.proj.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.ffn.net.2.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.30.ffn.net.2.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.30.norm2.bias": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.norm2.weight": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.30.scale_shift_table": "diffusion_pytorch_model-00009-of-00012.safetensors", "blocks.31.attn1.norm_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.norm_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.to_k.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.to_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.to_out.0.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.to_out.0.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.to_q.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.to_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.to_v.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn1.to_v.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.norm_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.norm_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.to_k.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.to_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.to_out.0.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.to_out.0.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.to_q.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.to_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.to_v.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.attn2.to_v.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.ffn.net.0.proj.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.ffn.net.0.proj.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.ffn.net.2.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.ffn.net.2.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.norm2.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.norm2.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.31.scale_shift_table": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.norm_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.norm_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.to_k.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.to_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.to_out.0.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.to_out.0.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.to_q.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.to_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.to_v.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn1.to_v.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.norm_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.norm_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.to_k.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.to_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.to_out.0.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.to_out.0.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.to_q.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.to_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.to_v.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.attn2.to_v.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.ffn.net.0.proj.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.ffn.net.0.proj.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.ffn.net.2.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.ffn.net.2.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.norm2.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.norm2.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.32.scale_shift_table": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.norm_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.norm_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.to_k.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.to_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.to_out.0.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.to_out.0.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.to_q.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.to_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.to_v.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn1.to_v.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.norm_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.norm_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.to_k.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.to_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.to_out.0.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.to_out.0.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.to_q.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.to_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.to_v.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.attn2.to_v.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.ffn.net.0.proj.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.ffn.net.0.proj.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.ffn.net.2.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.ffn.net.2.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.norm2.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.norm2.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.33.scale_shift_table": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.norm_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.norm_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.to_k.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.to_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.to_out.0.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.to_out.0.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.to_q.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.to_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.to_v.bias": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn1.to_v.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn2.norm_k.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn2.norm_q.weight": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.34.attn2.to_k.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.attn2.to_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.attn2.to_out.0.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.attn2.to_out.0.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.attn2.to_q.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.attn2.to_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.attn2.to_v.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.attn2.to_v.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.ffn.net.0.proj.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.ffn.net.0.proj.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.ffn.net.2.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.ffn.net.2.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.norm2.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.norm2.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.34.scale_shift_table": "diffusion_pytorch_model-00010-of-00012.safetensors", "blocks.35.attn1.norm_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.norm_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.to_k.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.to_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.to_out.0.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.to_out.0.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.to_q.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.to_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.to_v.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn1.to_v.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.norm_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.norm_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.to_k.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.to_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.to_out.0.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.to_out.0.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.to_q.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.to_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.to_v.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.attn2.to_v.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.ffn.net.0.proj.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.ffn.net.0.proj.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.ffn.net.2.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.ffn.net.2.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.norm2.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.norm2.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.35.scale_shift_table": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.norm_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.norm_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.to_k.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.to_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.to_out.0.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.to_out.0.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.to_q.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.to_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.to_v.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn1.to_v.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.norm_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.norm_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.to_k.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.to_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.to_out.0.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.to_out.0.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.to_q.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.to_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.to_v.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.attn2.to_v.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.ffn.net.0.proj.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.ffn.net.0.proj.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.ffn.net.2.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.ffn.net.2.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.norm2.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.norm2.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.36.scale_shift_table": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.norm_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.norm_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.to_k.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.to_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.to_out.0.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.to_out.0.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.to_q.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.to_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.to_v.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn1.to_v.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.norm_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.norm_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.to_k.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.to_k.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.to_out.0.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.to_out.0.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.to_q.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.to_q.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.to_v.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.attn2.to_v.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.ffn.net.0.proj.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.ffn.net.0.proj.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.ffn.net.2.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.37.ffn.net.2.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.37.norm2.bias": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.norm2.weight": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.37.scale_shift_table": "diffusion_pytorch_model-00011-of-00012.safetensors", "blocks.38.attn1.norm_k.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.norm_q.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.to_k.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.to_k.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.to_out.0.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.to_out.0.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.to_q.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.to_q.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.to_v.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn1.to_v.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.norm_k.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.norm_q.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.to_k.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.to_k.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.to_out.0.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.to_out.0.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.to_q.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.to_q.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.to_v.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.attn2.to_v.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.ffn.net.0.proj.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.ffn.net.0.proj.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.ffn.net.2.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.ffn.net.2.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.norm2.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.norm2.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.38.scale_shift_table": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.norm_k.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.norm_q.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.to_k.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.to_k.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.to_out.0.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.to_out.0.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.to_q.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.to_q.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.to_v.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn1.to_v.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.norm_k.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.norm_q.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.to_k.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.to_k.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.to_out.0.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.to_out.0.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.to_q.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.to_q.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.to_v.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.attn2.to_v.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.ffn.net.0.proj.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.ffn.net.0.proj.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.ffn.net.2.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.ffn.net.2.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.norm2.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.norm2.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.39.scale_shift_table": "diffusion_pytorch_model-00012-of-00012.safetensors", "blocks.4.attn1.norm_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.norm_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.to_k.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.to_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.to_out.0.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.to_out.0.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.to_q.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.to_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.to_v.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn1.to_v.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.norm_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.norm_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.to_k.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.to_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.to_out.0.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.to_out.0.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.to_q.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.to_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.to_v.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.attn2.to_v.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.ffn.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.ffn.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.ffn.net.2.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.ffn.net.2.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.norm2.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.norm2.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.4.scale_shift_table": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.norm_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.norm_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.to_k.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.to_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.to_out.0.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.to_out.0.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.to_q.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.to_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.to_v.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn1.to_v.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.norm_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.norm_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.to_k.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.to_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.to_out.0.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.to_out.0.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.to_q.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.to_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.to_v.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.attn2.to_v.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.ffn.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.ffn.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.ffn.net.2.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.ffn.net.2.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.norm2.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.norm2.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.5.scale_shift_table": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.norm_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.norm_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.to_k.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.to_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.to_out.0.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.to_out.0.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.to_q.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.to_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.to_v.bias": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn1.to_v.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn2.norm_k.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn2.norm_q.weight": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.6.attn2.to_k.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.attn2.to_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.attn2.to_out.0.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.attn2.to_out.0.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.attn2.to_q.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.attn2.to_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.attn2.to_v.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.attn2.to_v.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.ffn.net.0.proj.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.ffn.net.0.proj.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.ffn.net.2.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.ffn.net.2.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.norm2.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.norm2.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.6.scale_shift_table": "diffusion_pytorch_model-00002-of-00012.safetensors", "blocks.7.attn1.norm_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.norm_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.to_k.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.to_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.to_out.0.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.to_out.0.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.to_q.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.to_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.to_v.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn1.to_v.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.norm_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.norm_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.to_k.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.to_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.to_out.0.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.to_out.0.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.to_q.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.to_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.to_v.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.attn2.to_v.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.ffn.net.0.proj.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.ffn.net.0.proj.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.ffn.net.2.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.ffn.net.2.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.norm2.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.norm2.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.7.scale_shift_table": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.norm_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.norm_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.to_k.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.to_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.to_out.0.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.to_out.0.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.to_q.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.to_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.to_v.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn1.to_v.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.norm_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.norm_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.to_k.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.to_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.to_out.0.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.to_out.0.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.to_q.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.to_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.to_v.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.attn2.to_v.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.ffn.net.0.proj.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.ffn.net.0.proj.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.ffn.net.2.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.ffn.net.2.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.norm2.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.norm2.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.8.scale_shift_table": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.norm_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.norm_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.to_k.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.to_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.to_out.0.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.to_out.0.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.to_q.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.to_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.to_v.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn1.to_v.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.norm_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.norm_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.to_k.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.to_k.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.to_out.0.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.to_out.0.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.to_q.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.to_q.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.to_v.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.attn2.to_v.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.ffn.net.0.proj.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.ffn.net.0.proj.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.ffn.net.2.bias": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.9.ffn.net.2.weight": "diffusion_pytorch_model-00004-of-00012.safetensors", "blocks.9.norm2.bias": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.norm2.weight": "diffusion_pytorch_model-00003-of-00012.safetensors", "blocks.9.scale_shift_table": "diffusion_pytorch_model-00003-of-00012.safetensors", "condition_embedder.text_embedder.linear_1.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.text_embedder.linear_1.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.text_embedder.linear_2.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.text_embedder.linear_2.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.time_embedder.linear_1.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.time_embedder.linear_1.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.time_embedder.linear_2.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.time_embedder.linear_2.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.time_proj.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "condition_embedder.time_proj.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "patch_embedding.bias": "diffusion_pytorch_model-00001-of-00012.safetensors", "patch_embedding.weight": "diffusion_pytorch_model-00001-of-00012.safetensors", "proj_out.bias": "diffusion_pytorch_model-00012-of-00012.safetensors", "proj_out.weight": "diffusion_pytorch_model-00012-of-00012.safetensors", "scale_shift_table": "diffusion_pytorch_model-00001-of-00012.safetensors"}}